#!/usr/bin/env python3
"""
Startup script for the ATS Resume Analyzer with dependency installation
"""
import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    try:
        print("Installing dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        sys.exit(1)

def start_server():
    """Start the FastAPI server"""
    try:
        import uvicorn
        from main import app
        
        print("\nStarting ATS Resume Analyzer...")
        print("Server will be available at: http://localhost:8000")
        print("Press Ctrl+C to stop the server\n")
        
        uvicorn.run(
            "main:app", 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
    except ImportError:
        print("Dependencies not installed. Installing now...")
        install_requirements()
        start_server()

if __name__ == "__main__":
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("Error: requirements.txt not found!")
        sys.exit(1)
    
    # Try to start server, install deps if needed
    start_server()