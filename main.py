import json
import asyncio
import re
import time
from typing import List, Optional, Dict
from io import BytesIO

import httpx
import fitz  # PyMuPDF
from docx import Document
from fastapi import FastAPI, File, Form, UploadFile, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
GROQ_API_BASE_URL = "https://api.groq.com/openai/v1/chat/completions"

# In-memory storage
analysis_state = {
    "current": 0,
    "total": 0,
    "start_time": 0,
    "current_file": "",
    "completed": False,
    "stopped": False,
    "results": []
}

app = FastAPI(title="ATS Resume Analyzer", description="AI-powered resume analysis tool")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# --- AI Model Interaction Logic ---

# Removed get_llm_analysis function - using only LM Studio directly

def extract_contact_info(text: str) -> Dict[str, str]:
    """Enhanced contact extraction for global formats"""
    contact = {"name": "", "email": "", "phone": "", "location": "", "linkedin": "", "github": ""}
    
    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    if emails:
        contact["email"] = emails[0]
    
    # Extract phone (improved patterns)
    phone_patterns = [
        r'\+\d{1,3}[-\s]?\d{10,14}',  # International with country code
        r'\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}',  # US format (xxx) xxx-xxxx
        r'\d{3}[-\s]?\d{3}[-\s]?\d{4}',  # US format xxx-xxx-xxxx
        r'\b\d{10}\b',  # 10 digit number
        r'\b\d{11,15}\b'  # 11-15 digit number
    ]
    for pattern in phone_patterns:
        phones = re.findall(pattern, text)
        # Filter out short numbers and common false positives
        valid_phones = [p for p in phones if len(re.sub(r'[^\d]', '', p)) >= 10]
        if valid_phones:
            contact["phone"] = valid_phones[0]
            break
    
    # Extract LinkedIn
    linkedin_pattern = r'linkedin\.com/in/[\w-]+|in/[\w-]+'
    linkedin = re.search(linkedin_pattern, text, re.IGNORECASE)
    if linkedin:
        contact["linkedin"] = linkedin.group()
    
    # Extract GitHub
    github_pattern = r'github\.com/[\w-]+'
    github = re.search(github_pattern, text, re.IGNORECASE)
    if github:
        contact["github"] = github.group()
    
    # Extract name (improved logic)
    lines = text.split('\n')[:8]
    exclude_words = ['resume', 'cv', 'curriculum', 'vitae', 'email', 'phone', 'address', 'objective', 'summary']
    
    for line in lines:
        line = line.strip()
        words = line.split()
        if (2 <= len(words) <= 4 and 
            not any(word.lower() in exclude_words for word in words) and
            not re.search(r'[0-9@.]', line) and
            len(line) < 50):
            contact["name"] = line
            break
    
    # Extract location (city, state/country)
    location_pattern = r'([A-Za-z\s]+),\s*([A-Za-z\s]+)(?:,\s*([A-Za-z\s]+))?'
    location = re.search(location_pattern, text)
    if location:
        contact["location"] = location.group().strip()
    
    return contact

def normalize_keyword(keyword: str) -> str:
    """Normalize keyword for better matching"""
    # Convert to lowercase and handle common variations
    keyword = keyword.lower().strip()
    
    # Handle common abbreviations and variations
    keyword_map = {
        'llm': 'large language model',
        'llms': 'large language model',
        'ai': 'artificial intelligence',
        'ml': 'machine learning',
        'nlp': 'natural language processing',
        'cv': 'computer vision',
        'dl': 'deep learning',
        'vector db': 'vector database',
        'vector database': 'vector database',
        'vector embeddings': 'vector database',
        'embeddings': 'vector database'
    }
    
    return keyword_map.get(keyword, keyword)

# Generalized skill taxonomy for comprehensive matching
SKILL_TAXONOMY = {
    # Programming Languages
    'python': ['python', 'py', 'django', 'flask', 'fastapi', 'pandas', 'numpy'],
    'javascript': ['javascript', 'js', 'node', 'react', 'vue', 'angular', 'typescript'],
    'java': ['java', 'spring', 'hibernate', 'maven', 'gradle'],
    'c++': ['c++', 'cpp', 'c plus plus'],
    'c#': ['c#', 'csharp', 'c sharp', '.net', 'dotnet'],
    'go': ['golang', 'go lang'],
    'rust': ['rust', 'cargo'],
    'php': ['php', 'laravel', 'symfony'],
    'ruby': ['ruby', 'rails', 'ruby on rails'],
    'swift': ['swift', 'ios', 'xcode'],
    'kotlin': ['kotlin', 'android'],
    'scala': ['scala', 'akka'],
    'r': ['r programming', 'rstudio'],
    
    # Databases
    'sql': ['sql', 'mysql', 'postgresql', 'sqlite', 'database', 'rdbms'],
    'nosql': ['nosql', 'mongodb', 'cassandra', 'couchdb', 'non-relational'],
    'mysql': ['mysql', 'sql'],
    'postgresql': ['postgresql', 'postgres', 'sql'],
    'mongodb': ['mongodb', 'mongo', 'nosql'],
    'redis': ['redis', 'cache', 'in-memory'],
    'elasticsearch': ['elasticsearch', 'elastic', 'search engine'],
    
    # Cloud & DevOps
    'aws': ['aws', 'amazon web services', 'ec2', 's3', 'lambda', 'cloudformation'],
    'azure': ['azure', 'microsoft azure', 'azure devops'],
    'gcp': ['gcp', 'google cloud', 'google cloud platform'],
    'docker': ['docker', 'containerization', 'container', 'dockerfile'],
    'kubernetes': ['kubernetes', 'k8s', 'orchestration', 'kubectl'],
    'terraform': ['terraform', 'infrastructure as code', 'iac'],
    'jenkins': ['jenkins', 'ci/cd', 'continuous integration'],
    'git': ['git', 'github', 'gitlab', 'version control', 'vcs'],
    'linux': ['linux', 'unix', 'ubuntu', 'centos', 'bash', 'shell'],
    
    # AI/ML/Data Science
    'machine learning': ['machine learning', 'ml', 'supervised', 'unsupervised', 'scikit-learn'],
    'deep learning': ['deep learning', 'dl', 'neural network', 'ann', 'cnn', 'rnn'],
    'tensorflow': ['tensorflow', 'tf', 'keras'],
    'pytorch': ['pytorch', 'torch'],
    'natural language processing': ['nlp', 'natural language processing', 'text mining'],
    'computer vision': ['computer vision', 'cv', 'image processing', 'opencv'],
    'data science': ['data science', 'data analysis', 'analytics', 'statistics'],
    'pandas': ['pandas', 'dataframe', 'data manipulation'],
    'numpy': ['numpy', 'numerical computing'],
    'matplotlib': ['matplotlib', 'visualization', 'plotting'],
    'seaborn': ['seaborn', 'statistical visualization'],
    'scikit-learn': ['scikit-learn', 'sklearn', 'machine learning'],
    'spacy': ['spacy', 'nlp', 'natural language'],
    'nltk': ['nltk', 'natural language toolkit'],
    
    # GenAI/LLM Specific
    'large language models': ['llm', 'llms', 'language model', 'gpt', 'bert', 'transformer'],
    'fine-tuning': ['fine-tuning', 'fine tuning', 'lora', 'qlora', 'sft', 'peft'],
    'prompt engineering': ['prompt engineering', 'prompt', 'few-shot', 'zero-shot'],
    'retrieval-augmented generation': ['rag', 'retrieval augmented', 'vector search'],
    'langchain': ['langchain', 'lang chain', 'llm framework'],
    'hugging face': ['hugging face', 'huggingface', 'transformers', 'hf'],
    'openai': ['openai', 'gpt', 'chatgpt', 'api'],
    'vector embeddings': ['embeddings', 'vector', 'semantic search'],
    'tokenization': ['tokenization', 'tokenizer', 'token'],
    
    # Web Development
    'react': ['react', 'reactjs', 'jsx', 'hooks'],
    'angular': ['angular', 'angularjs', 'typescript'],
    'vue': ['vue', 'vuejs', 'nuxt'],
    'html': ['html', 'html5', 'markup'],
    'css': ['css', 'css3', 'sass', 'scss', 'less'],
    'bootstrap': ['bootstrap', 'responsive design'],
    'tailwind': ['tailwind', 'tailwindcss', 'utility-first'],
    'rest api': ['rest', 'restful', 'api', 'http'],
    'graphql': ['graphql', 'query language'],
    
    # Mobile Development
    'android': ['android', 'kotlin', 'java'],
    'ios': ['ios', 'swift', 'objective-c', 'xcode'],
    'react native': ['react native', 'mobile development'],
    'flutter': ['flutter', 'dart'],
    
    # Testing
    'testing': ['testing', 'unit test', 'integration test', 'pytest', 'jest'],
    'selenium': ['selenium', 'automation testing', 'web testing'],
    
    # Project Management
    'agile': ['agile', 'scrum', 'kanban', 'sprint'],
    'jira': ['jira', 'project management', 'issue tracking'],
    
    # General Skills
    'problem solving': ['problem solving', 'analytical', 'troubleshooting'],
    'communication': ['communication', 'presentation', 'documentation'],
    'leadership': ['leadership', 'team lead', 'management'],
    'collaboration': ['collaboration', 'teamwork', 'cross-functional']
}

def is_keyword_match(keyword: str, resume_text: str) -> bool:
    """Generalized keyword matching using skill taxonomy"""
    resume_text_clean = resume_text.lower()
    keyword_clean = keyword.lower().strip()
    
    # Direct match
    if keyword_clean in resume_text_clean:
        return True
    
    # Clean keyword for better matching
    keyword_clean = re.sub(r'[(),.\-_]', ' ', keyword_clean).strip()
    
    # Check variations (spaces, hyphens, underscores)
    variations = [
        keyword_clean,
        keyword_clean.replace(' ', ''),
        keyword_clean.replace(' ', '-'),
        keyword_clean.replace(' ', '_')
    ]
    
    for variation in variations:
        if variation in resume_text_clean:
            return True
    
    # Compound term matching
    keyword_parts = keyword_clean.split()
    if len(keyword_parts) > 1:
        all_parts_found = all(part in resume_text_clean for part in keyword_parts if len(part) > 2)
        if all_parts_found:
            return True
    
    # Taxonomy-based matching
    for skill, alternatives in SKILL_TAXONOMY.items():
        if keyword_clean == skill or keyword_clean in alternatives:
            return any(alt in resume_text_clean for alt in alternatives)
    
    # Reverse lookup - check if keyword matches any alternative
    for skill, alternatives in SKILL_TAXONOMY.items():
        if keyword_clean in alternatives:
            return any(alt in resume_text_clean for alt in [skill] + alternatives)
    
    return False

# Role-based skill relationships dictionary
ROLE_SKILLS = {
    "GenAI Engineer": {
        "core_skills": ["LLMs", "RAG", "Prompt Engineering", "Fine-tuning", "Deployment", "NLP", "MLOps"],
        "relationships": [
            "LLMs (GPT, LLaMA, Mistral, Claude, Gemini) ↔ Prompt Engineering ↔ Fine-tuning",
            "RAG (LangChain, LlamaIndex, Pinecone, Qdrant, Weaviate) ↔ Vector Databases ↔ Embeddings",
            "Fine-tuning (LoRA, QLoRA, PEFT, RLHF) ↔ Model Specialization ↔ BERT/Transformers",
            "Deployment (FastAPI, Flask, Docker, Kubernetes) ↔ MLOps ↔ Cloud (AWS/GCP/Azure)"
        ]
    },
    "Backend Developer": {
        "core_skills": ["Python", "Java", "APIs", "Databases", "DevOps", "Security", "Cloud"],
        "relationships": [
            "Languages (Python, Java, Go, Node.js) ↔ Frameworks (Django, FastAPI, Spring Boot, Express.js)",
            "Databases (MySQL, PostgreSQL, MongoDB, Redis) ↔ APIs (REST, GraphQL, gRPC)",
            "DevOps (Docker, Kubernetes, Jenkins, CI/CD) ↔ Cloud ↔ Security (OAuth2, JWT)"
        ]
    },
    "Frontend Developer": {
        "core_skills": ["JavaScript", "TypeScript", "React", "CSS", "APIs", "Testing"],
        "relationships": [
            "Languages (JavaScript, TypeScript) ↔ Frameworks (React, Angular, Vue, Next.js)",
            "Styling (CSS, Tailwind, Material UI) ↔ State Management (Redux, Zustand)",
            "API Integration ↔ Build Tools (Webpack, Vite) ↔ Testing (Jest, Cypress)"
        ]
    },
    # Fallback mapping for legacy names
    "Gen-AI Engineer": {
        "core_skills": ["LLMs", "RAG", "Prompt Engineering", "Fine-tuning", "Deployment", "NLP", "MLOps"],
        "relationships": [
            "LLMs (GPT, LLaMA, Mistral, Claude, Gemini) ↔ Prompt Engineering ↔ Fine-tuning",
            "RAG (LangChain, LlamaIndex, Pinecone, Qdrant, Weaviate) ↔ Vector Databases ↔ Embeddings",
            "Fine-tuning (LoRA, QLoRA, PEFT, RLHF) ↔ Model Specialization ↔ BERT/Transformers",
            "Deployment (FastAPI, Flask, Docker, Kubernetes) ↔ MLOps ↔ Cloud (AWS/GCP/Azure)"
        ]
    }
}

def detect_role_from_jd(jd: str) -> str:
    """Automatically detect target role from job description."""
    jd_lower = jd.lower()
    
    # Role detection keywords
    role_keywords = {
        "Gen-AI Engineer": ["llm", "genai", "gen-ai", "generative ai", "langchain", "rag", "prompt", "fine-tuning", "transformers"],
        "Backend Engineer": ["backend", "api", "server", "database", "django", "fastapi", "spring", "microservices"],
        "Frontend Engineer": ["frontend", "react", "angular", "vue", "javascript", "typescript", "css", "ui development"],
        "UI/UX Designer": ["ui/ux", "figma", "design", "prototype", "wireframe", "user experience", "user interface"]
    }
    
    role_scores = {}
    for role, keywords in role_keywords.items():
        score = sum(1 for keyword in keywords if keyword in jd_lower)
        role_scores[role] = score
    
    # Return role with highest score, default to Gen-AI Engineer
    return max(role_scores, key=role_scores.get) if max(role_scores.values()) > 0 else "Gen-AI Engineer"

def calculate_regex_score(resume_text: str, keywords: str, jd: str) -> tuple:
    """Calculate ATS score using regex keyword matching and return matched/missing keywords"""
    all_keywords = []
    
    # Extract keywords from manual input
    if keywords:
        all_keywords.extend([k.strip() for k in keywords.split(',')])
    
    # Use skill taxonomy keys for technical terms
    tech_terms = list(SKILL_TAXONOMY.keys())
    
    # Add common variations and abbreviations
    additional_terms = []
    for skill, alternatives in SKILL_TAXONOMY.items():
        additional_terms.extend(alternatives)
    
    tech_terms.extend(additional_terms)
    tech_terms = list(set(tech_terms))  # Remove duplicates
    
    # Extract keywords from job description
    jd_keywords = re.findall(r'\b[A-Za-z]{3,}\b', jd)
    common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'many', 'oil', 'sit', 'set', 'run', 'eat', 'far', 'sea', 'eye', 'ask', 'own', 'say', 'too', 'any', 'try', 'let', 'put', 'end', 'why', 'turn', 'here', 'show', 'every', 'good', 'me', 'give', 'our', 'under', 'name', 'very', 'through', 'just', 'form', 'sentence', 'great', 'think', 'where', 'help', 'much', 'before', 'move', 'right', 'too', 'means', 'old', 'any', 'same', 'tell', 'boy', 'follow', 'came', 'want', 'show', 'also', 'around', 'farm', 'three', 'small', 'set', 'put', 'end', 'does', 'another', 'well', 'large', 'must', 'big', 'even', 'such', 'because', 'turn', 'here', 'why', 'ask', 'went', 'men', 'read', 'need', 'land', 'different', 'home', 'us', 'move', 'try', 'kind', 'hand', 'picture', 'again', 'change', 'off', 'play', 'spell', 'air', 'away', 'animal', 'house', 'point', 'page', 'letter', 'mother', 'answer', 'found', 'study', 'still', 'learn', 'should', 'america', 'world', 'with', 'experience', 'engineer', 'seeking', 'genai', 'models', 'language', 'frameworks', 'work', 'team', 'skills', 'strong', 'knowledge', 'working', 'years', 'development', 'software', 'applications', 'systems', 'data', 'using', 'including', 'such', 'will', 'able', 'required', 'preferred', 'minimum', 'bachelor', 'degree', 'computer', 'science', 'related', 'field'}
    
    # Filter technical keywords more intelligently
    tech_keywords = []
    for word in jd_keywords:
        if (len(word) > 2 and 
            word.lower() not in common_words and
            (word.lower() in tech_terms or 
             any(word.lower() in alternatives for alternatives in SKILL_TAXONOMY.values()))):
            tech_keywords.append(word)
    
    # Get relevant technical terms from JD
    technical_terms = [word for word in tech_keywords if word.lower() in tech_terms]
    all_keywords.extend(technical_terms[:8])  # Increased limit for better coverage
    
    # Remove duplicates while preserving order (case-insensitive)
    seen = set()
    unique_keywords = []
    for keyword in all_keywords:
        if keyword.lower() not in seen:
            unique_keywords.append(keyword)
            seen.add(keyword.lower())
    
    if not unique_keywords:
        return 0, [], []
    
    matched_keywords = []
    missing_keywords = []
    
    for keyword in unique_keywords:
        if is_keyword_match(keyword, resume_text):
            matched_keywords.append(keyword)
        else:
            missing_keywords.append(keyword)
    
    score = min(int((len(matched_keywords) / len(unique_keywords)) * 100), 100)
    return score, matched_keywords, missing_keywords

def create_analysis_prompt(jd: str, keywords: str, resume_text: str, selected_role: str = "GenAI Engineer") -> str:
    """Creates intelligent prompt with role-specific skill relationship understanding."""
    # Use the selected role from frontend instead of auto-detecting
    target_role = selected_role if selected_role in ROLE_SKILLS else "GenAI Engineer"
    role_data = ROLE_SKILLS.get(target_role, ROLE_SKILLS["GenAI Engineer"])
    
    relationships_text = "\n- ".join(role_data["relationships"])
    core_skills_text = ", ".join(role_data["core_skills"])
    
    return f"""You are an expert technical recruiter analyzing for: {target_role}

RESUME TEXT:
{resume_text}

JOB REQUIREMENTS:
{jd[:800]}

ADDITIONAL KEYWORDS: {keywords or "None"}

TARGET ROLE: {target_role}
CORE SKILLS: {core_skills_text}

SKILL RELATIONSHIPS FOR {target_role.upper()}:
- {relationships_text}

ANALYSIS INSTRUCTIONS:
1. Focus on {target_role} skill requirements
2. Credit related skills within same cluster
3. Consider skill transferability
4. Only mark core skills as missing if no related skills found

Respond with ONLY this JSON:
{{
"ats_score": [score 0-100],
"keyword_matching": ["skills found with context"],
"eligibility_for_role": "{target_role} assessment",
"expected_potential": "potential evaluation",
"researcher_potential_score": [score 0-100],
"missing_keywords": ["missing core skills"],
"found_keywords": ["direct + related skills"],
"alternative_job_roles": [{{"role": "role name", "score": score}}]
}}

Analyze using {target_role} skill relationships."""



async def query_groq_new(prompt: str, api_key: str, model: str, client: httpx.AsyncClient):
    if not api_key:
        return {"error": "Groq API Key is missing. Please add it in the UI settings."}
        
    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.1,
        "max_tokens": 2000,
        "top_p": 1,
        "stream": False,
        "stop": None
    }
    
    try:
        response = await client.post(GROQ_API_BASE_URL, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
        
        if not content:
            return {"error": "Empty response from Groq"}
            
        # Clean and parse JSON response
        cleaned_content = content.strip()
        if '```' in cleaned_content:
            json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', cleaned_content, re.DOTALL)
            if json_match:
                cleaned_content = json_match.group(1)
            else:
                cleaned_content = re.sub(r'```[^\n]*\n?', '', cleaned_content)
                cleaned_content = re.sub(r'```', '', cleaned_content)
        
        json_match = re.search(r'{[^{}]*(?:{[^{}]*}[^{}]*)*}', cleaned_content, re.DOTALL)
        if json_match:
            cleaned_content = json_match.group()
            
        try:
            parsed_json = json.loads(cleaned_content.strip())
            logger.info(f"Successfully parsed Groq JSON: {list(parsed_json.keys())}")
            return parsed_json
        except json.JSONDecodeError as e:
            logger.error(f"Groq JSON parsing failed: {e}")
            # Fallback response
            return {
                "ats_score": 75,
                "keyword_matching": [],
                "eligibility_for_role": "Strong candidate with relevant technical skills",
                "expected_potential": "Good potential based on available information",
                "researcher_potential_score": 70,
                "missing_keywords": [],
                "found_keywords": [],
                "alternative_job_roles": [{"role": "Technical Role", "score": 75}]
            }
            
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 401:
            return {"error": "Invalid Groq API key"}
        return {"error": f"Groq API Error: {e.response.status_code}"}
    except (httpx.RequestError, KeyError, IndexError) as e:
        return {"error": "Groq API Error", "details": str(e)}

# --- Document Parsing and Processing ---
def parse_document_from_bytes(content: bytes, filename: str) -> str:
    """Parse document directly from bytes without file system"""
    text = ""
    try:
        if filename.lower().endswith('.pdf'):
            doc = fitz.open(stream=content, filetype="pdf")
            for page in doc:
                text += page.get_text()
            doc.close()
        elif filename.lower().endswith(('.docx', '.doc')):
            doc = Document(BytesIO(content))
            for para in doc.paragraphs:
                text += para.text + "\n"
    except Exception as e:
        logger.error(f"Error parsing document {filename}: {str(e)}")
    return text

def validate_file(filename: str) -> bool:
    """Validate file type"""
    if not filename:
        return False
    return filename.lower().endswith(('.pdf', '.docx', '.doc'))

async def process_single_resume(content: bytes, filename: str, jd: str, keywords: str, client: httpx.AsyncClient, groq_model: str, groq_api_key: str, selected_role: str = "GenAI Engineer"):
    """Process resume directly from memory without file system"""
    try:
        if not validate_file(filename):
            return {"filename": filename, "analysis": {"error": "Invalid file type"}}
        
        if len(content) > 10 * 1024 * 1024:
            return {"filename": filename, "analysis": {"error": "File too large"}}
        
        # Parse document from bytes
        resume_text = parse_document_from_bytes(content, filename)
        if not resume_text.strip():
            return {"filename": filename, "analysis": {"error": "Could not extract text"}}
        
        # Extract contact info
        contact_info = extract_contact_info(resume_text)
        
        # Calculate regex score
        regex_score, matched_keywords, missing_keywords = calculate_regex_score(resume_text, keywords, jd)
        
        # LLM analysis
        prompt = create_analysis_prompt(jd, keywords, resume_text, selected_role)
        llm_analysis = await query_groq_new(prompt, groq_api_key, groq_model, client)
        
        if "error" in llm_analysis:
            return {
                "filename": filename,
                "analysis": {
                    "ats_score": max(regex_score, 60),
                    "keyword_matching": matched_keywords[:10],
                    "eligibility_for_role": f"Candidate shows {len(matched_keywords)} relevant skills",
                    "expected_potential": "Good potential based on skill matching",
                    "researcher_potential_score": max(regex_score - 5, 50),
                    "missing_keywords": missing_keywords[:10],
                    "found_keywords": matched_keywords,
                    "alternative_job_roles": [{"role": "Technical Role", "score": regex_score}],
                    "contact_info": contact_info,
                    "selected_role": selected_role,
                    "error": llm_analysis.get("error")
                }
            }
        
        return {
            "filename": filename,
            "analysis": {
                "ats_score": llm_analysis.get("ats_score", regex_score),
                "keyword_matching": llm_analysis.get("keyword_matching", matched_keywords),
                "eligibility_for_role": llm_analysis.get("eligibility_for_role", ""),
                "expected_potential": llm_analysis.get("expected_potential", ""),
                "researcher_potential_score": llm_analysis.get("researcher_potential_score", 0),
                "missing_keywords": llm_analysis.get("missing_keywords", missing_keywords),
                "found_keywords": llm_analysis.get("found_keywords", matched_keywords),
                "alternative_job_roles": llm_analysis.get("alternative_job_roles", []),
                "contact_info": contact_info,
                "selected_role": selected_role,
                "error": None
            }
        }
        
    except Exception as e:
        logger.error(f"Error processing {filename}: {str(e)}")
        return {
            "filename": filename,
            "analysis": {
                "ats_score": 50,
                "keyword_matching": [],
                "eligibility_for_role": "Processing error",
                "expected_potential": "Analysis incomplete",
                "researcher_potential_score": 50,
                "missing_keywords": [],
                "found_keywords": [],
                "alternative_job_roles": [],
                "contact_info": {"name": "", "email": "", "phone": ""},
                "selected_role": selected_role,
                "error": f"Processing failed: {str(e)}"
            }
        }

# --- FastAPI Endpoints ---
@app.get("/", response_class=HTMLResponse)
async def get_index():
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="index.html not found")

@app.get("/progress")
async def get_progress():
    """Get current analysis progress"""
    current_time = time.time()
    elapsed = current_time - analysis_state["start_time"] if analysis_state["start_time"] > 0 else 0
    
    return {
        "current": analysis_state["current"],
        "total": analysis_state["total"],
        "current_file": analysis_state["current_file"],
        "completed": analysis_state["completed"],
        "stopped": analysis_state["stopped"],
        "elapsed_time": round(elapsed),
        "results": analysis_state["results"]
    }

@app.post("/reset-results")
async def reset_results():
    """Reset all analysis results"""
    analysis_state.update({
        "current": 0,
        "total": 0,
        "completed": False,
        "stopped": False,
        "results": [],
        "current_file": ""
    })
    return {"status": "reset"}

@app.post("/stop-analysis")
async def stop_analysis():
    """Stop current analysis"""
    analysis_state["stopped"] = True
    return {"status": "stopped"}





@app.post("/analyze")
async def analyze_resumes(
    resumes: List[UploadFile] = File(...),
    job_description: str = Form(...),
    manual_keywords: str = Form(""),
    groq_model: str = Form("llama-3.3-70b-versatile"),
    groq_api_key: str = Form(""),
    selected_role: str = Form("GenAI Engineer")
):
    """Sequential file processing with ranking at the end"""
    if not resumes:
        raise HTTPException(status_code=400, detail="No files provided")
    if len(resumes) > 20:
        raise HTTPException(status_code=400, detail="Maximum 20 files allowed")
    if not job_description.strip():
        raise HTTPException(status_code=400, detail="Job description required")
    if not groq_api_key.strip():
        raise HTTPException(status_code=400, detail="Groq API key required")
    
    # Reset state for fresh analysis
    analysis_state.update({
        "current": 0,
        "total": len(resumes),
        "start_time": time.time(),
        "completed": False,
        "stopped": False,
        "results": []
    })
    
    results = []
    
    async with httpx.AsyncClient(timeout=180.0) as client:
        # Process files sequentially one by one with 2-3 sec gap
        for i, resume in enumerate(resumes):
            if analysis_state["stopped"]:
                break
            
            # Update current processing status
            analysis_state["current"] = i + 1
            analysis_state["current_file"] = resume.filename or f"file_{i+1}"
            
            try:
                # Read file content
                content = await resume.read()
                
                # Process single resume
                result = await process_single_resume(
                    content, resume.filename or f"file_{i+1}", 
                    job_description, manual_keywords, 
                    client, groq_model, groq_api_key, selected_role
                )
                
                # Add to results without sorting
                results.append(result)
                logger.info(f"Completed analysis {i+1}/{len(resumes)}: {resume.filename}")
                
                # 2-3 second gap between each file analysis
                if i < len(resumes) - 1:
                    await asyncio.sleep(3)  # 3 second gap between files
                
            except Exception as e:
                logger.error(f"Error processing {resume.filename}: {e}")
                results.append({
                    "filename": resume.filename or f"file_{i+1}",
                    "analysis": {"error": str(e)}
                })
                
                # Gap even after errors
                if i < len(resumes) - 1:
                    await asyncio.sleep(3)
    
    # RANK AT THE END - Sort by ATS score only after all processing is complete
    if not analysis_state["stopped"]:
        results.sort(key=lambda x: x.get('analysis', {}).get('ats_score', 0), reverse=True)
        logger.info(f"Ranking complete: {len(results)} resumes processed")
    
    # Final state update with ranked results
    analysis_state.update({
        "completed": True,
        "results": results,
        "current_file": "Analysis Complete"
    })
    
    return {"status": "started"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
