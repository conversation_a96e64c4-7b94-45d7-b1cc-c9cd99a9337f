<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>LawVriks ATS Analyser Pro</title>
    <style>
      :root {
        /* Modern Professional: Contemporary design with excellent contrast */
        --bg: #0a0e1a;            /* deep navy */
        --panel: rgba(15, 23, 42, 0.75);
        --text: #f1f5f9;
        --muted: #94a3b8;
        --primary: #3b82f6;       /* modern blue */
        --primary-600: #2563eb;   /* deeper blue */
        --accent: #f59e0b;        /* warm amber */
        --danger: #ef4444;
        --warning: #f59e0b;
        --success: #10b981;
        --border: rgba(148, 163, 184, 0.2);
        --card: rgba(30, 41, 59, 0.6);
        --chip: rgba(51, 65, 85, 0.7);
        --shadow: 0 25px 50px rgba(0,0,0,.6);
        --blur: saturate(180%) blur(12px);
        --grad: linear-gradient(135deg, rgba(59,130,246,.15), rgba(245,158,11,.08));
        --grad-hover: linear-gradient(135deg, rgba(59,130,246,.22), rgba(245,158,11,.12));
        --glass-border: linear-gradient(135deg, rgba(255,255,255,.15), rgba(255,255,255,.05));
        --shimmer: linear-gradient(90deg, transparent, rgba(255,255,255,.15), transparent);
        --modal-backdrop: rgba(0,0,0,.8);
      }

      /* Modern Themes */
      [data-theme="Forest"] {
        --bg: #0f1419;
        --panel: rgba(20, 83, 45, 0.75);
        --card: rgba(34, 197, 94, 0.1);
        --primary: #22c55e;
        --primary-600: #16a34a;
        --accent: #fbbf24;
        --grad: linear-gradient(135deg, rgba(34,197,94,.15), rgba(251,191,36,.08));
        --grad-hover: linear-gradient(135deg, rgba(34,197,94,.22), rgba(251,191,36,.12));
      }
      [data-theme="Sacred"] {
        --bg: #1a0f1f;
        --panel: rgba(88, 28, 135, 0.75);
        --card: rgba(147, 51, 234, 0.1);
        --primary: #a855f7;
        --primary-600: #9333ea;
        --accent: #06b6d4;
        --grad: linear-gradient(135deg, rgba(168,85,247,.15), rgba(6,182,212,.08));
        --grad-hover: linear-gradient(135deg, rgba(168,85,247,.22), rgba(6,182,212,.12));
      }
      [data-theme="Ocean"] {
        --bg: #0c1420;
        --panel: rgba(14, 116, 144, 0.75);
        --card: rgba(6, 182, 212, 0.1);
        --primary: #06b6d4;
        --primary-600: #0891b2;
        --accent: #f97316;
        --grad: linear-gradient(135deg, rgba(6,182,212,.15), rgba(249,115,22,.08));
        --grad-hover: linear-gradient(135deg, rgba(6,182,212,.22), rgba(249,115,22,.12));
      }

      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        font-family: ui-sans-serif, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
        background: var(--bg);
        color: var(--text);
      }
      a { color: var(--accent); text-decoration: none; }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 16px;
      }
      header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        padding: 16px;
        background: var(--panel);
        backdrop-filter: var(--blur);
        -webkit-backdrop-filter: var(--blur);
        border-bottom: 1px solid var(--border);
        position: sticky;
        top: 0;
        z-index: 50;
      }
      header .title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 700;
        letter-spacing: .3px;
      }
      .badge {
        background: linear-gradient(135deg, var(--primary), var(--accent));
        color: #0b0f14;
        padding: 2px 8px;
        border-radius: 999px;
        font-size: 12px;
        font-weight: 700;
      }
      .controls { display: flex; gap: 12px; align-items: center; }
      select, input[type="text"], input[type="password"], input[type="file"], input[type="email"], input[type="tel"], textarea {
        background: var(--card);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 8px 10px;
        border-radius: 10px;
      }
      .grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
      }
      .panel {
        background: var(--panel);
        border: 1px solid var(--border);
        border-radius: 14px;
        box-shadow: var(--shadow);
        backdrop-filter: var(--blur);
        -webkit-backdrop-filter: var(--blur);
        background-image: var(--grad);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
      }
      .panel::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--shimmer);
        transition: left 0.5s ease;
        pointer-events: none;
      }
      .panel:hover {
        background-image: var(--grad-hover);
        transform: translateY(-2px);
        box-shadow: 0 28px 56px rgba(0,0,0,.6);
      }
      .panel:hover::before {
        left: 100%;
      }
      .section-title {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border);
        font-weight: 700;
      }
      .panel-body { padding: 14px; }

      .row { display: grid; grid-template-columns: 1fr; gap: 12px; }
      @media (min-width: 640px) { .row.two { grid-template-columns: 1fr 1fr; } }

      .btn {
        background: var(--card);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 10px 16px;
        border-radius: 12px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
      }
      .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--shimmer);
        transition: left 0.5s ease;
        pointer-events: none;
      }
      .btn:hover::before {
        left: 100%;
      }
      .btn.primary {
        background: var(--primary-600);
        color: white;
        border-color: transparent;
        font-weight: 600;
        box-shadow: 0 4px 14px rgba(59,130,246,.3);
      }
      .btn.ghost {
        background: transparent;
        border-color: var(--border);
      }
      .btn.warning {
        background: var(--warning);
        color: #1f2937;
        border-color: transparent;
        font-weight: 600;
      }
      .btn.danger {
        background: var(--danger);
        color: white;
        border-color: transparent;
        font-weight: 600;
      }
      .btn:disabled {
        opacity: .5;
        cursor: not-allowed;
        transform: none !important;
      }
      .btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59,130,246,.4);
      }
      .btn.primary:hover:not(:disabled) {
        background: var(--primary);
        box-shadow: 0 12px 35px rgba(59,130,246,.5);
      }
      .btn.ghost:hover:not(:disabled) {
        background: var(--card);
        border-color: var(--primary);
      }
      .btn.warning:hover:not(:disabled) {
        background: #f59e0b;
        box-shadow: 0 8px 25px rgba(245,158,11,.4);
      }
      .btn.danger:hover:not(:disabled) {
        background: #dc2626;
        box-shadow: 0 8px 25px rgba(239,68,68,.4);
      }
      #settingsBtn {
        font-size: 20px;
        padding: 12px;
        border-radius: 50%;
        transition: all 0.3s ease;
        background: var(--card);
        border: 2px solid var(--border);
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }
      #settingsBtn::before {
        content: '';
        position: absolute;
        inset: 0;
        background: var(--grad);
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 50%;
      }
      #settingsBtn:hover::before {
        opacity: 1;
      }
      #settingsBtn:hover {
        background: var(--primary-600);
        color: white;
        transform: rotate(180deg) scale(1.1);
        box-shadow: 0 8px 25px rgba(59,130,246,.4);
        border-color: var(--primary);
      }
      #settingsBtn:active {
        transform: rotate(180deg) scale(0.95);
      }

      .chips { display: flex; flex-wrap: wrap; gap: 8px; }
      .chip {
        background: var(--chip);
        border: 1px solid var(--border);
        color: var(--muted);
        padding: 4px 8px;
        border-radius: 999px;
        font-size: 12px;
      }

      /* Flip cards for Role Management */
      .cards { display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 12px; }
      /* (removed flip views) */
      .card {
        perspective: 1000px;
      }
      .card-inner {
        position: relative;
        width: 100%;
        height: 180px;
        transform-style: preserve-3d;
        transition: transform .5s ease;
      }
      .card.flipped .card-inner { transform: rotateY(180deg); }
      .card-face {
        position: absolute; inset: 0;
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 12px;
        backface-visibility: hidden;
        display: flex; flex-direction: column; gap: 8px;
      }
      .card-face.back { transform: rotateY(180deg); }

      /* Progress */
      .progress-wrap { display: flex; align-items: center; gap: 12px; }
      .progress {
        flex: 1;
        height: 10px;
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 999px;
        overflow: hidden;
      }
      .progress > div { height: 100%; width: 0%; background: linear-gradient(90deg, var(--primary), var(--accent)); transition: width .3s ease; }

      /* Table */
      table { width: 100%; border-collapse: collapse; }
      th, td { border-bottom: 1px solid var(--border); padding: 10px; text-align: left; }
      th { color: var(--muted); cursor: pointer; }
      tbody tr:hover { background: rgba(255,255,255,.03); }

      .kbd { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; background: var(--card); border: 1px solid var(--border); padding: 2px 6px; border-radius: 6px; color: var(--muted); font-size: 12px; }

      /* Circular score */
      .circle {
        width: 64px; height: 64px; border-radius: 50%;
        background: conic-gradient(var(--primary) calc(var(--val)*1%), rgba(255,255,255,0.08) 0);
        display: grid; place-items: center;
        border: 1px solid var(--border);
      }
      .circle span { font-weight: 700; font-size: 14px; }

      /* Enhanced Drop Zone */
      #dropZone {
        transition: all 0.3s ease;
        background: var(--card);
        border: 2px dashed var(--border);
        position: relative;
        overflow: hidden;
      }
      #dropZone::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--grad);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }
      #dropZone:hover::before {
        opacity: 1;
      }
      #dropZone.drag-over {
        border-color: var(--primary-600);
        background: var(--grad-hover);
        transform: scale(1.02);
        box-shadow: 0 12px 28px rgba(68,226,172,.25);
      }

      /* Enhanced Modal System */
      .modal-backdrop {
        position: fixed;
        inset: 0;
        background: var(--modal-backdrop);
        display: none;
        z-index: 1000;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      .modal-backdrop.show {
        display: block;
        opacity: 1;
      }
      .modal {
        position: fixed;
        inset: 0;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1001;
        padding: 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      .modal.show {
        display: flex;
        opacity: 1;
      }
      .modal .content {
        width: min(600px, 95vw);
        max-height: 90vh;
        overflow-y: auto;
        background: var(--panel);
        border: 2px solid var(--border);
        border-radius: 16px;
        box-shadow: var(--shadow), 0 0 0 1px rgba(255,255,255,0.1);
        padding: 24px;
        backdrop-filter: var(--blur);
        -webkit-backdrop-filter: var(--blur);
        background-image: var(--grad);
        transform: translateY(-20px) scale(0.95);
        transition: transform 0.3s ease;
        position: relative;
      }
      .modal.show .content {
        transform: translateY(0) scale(1);
      }
      .modal .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border);
      }
      .modal .header strong {
        font-size: 18px;
        color: var(--text);
      }
      @keyframes modalSlideIn {
        from {
          opacity: 0;
          transform: translateY(-30px) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
      .modal .header { display:flex; align-items:center; justify-content: space-between; margin-bottom: 8px; }
      .grid-two { display:grid; grid-template-columns: 1fr; gap: 10px; }
      @media (min-width: 900px) { .grid-two { grid-template-columns: 1fr 1fr; } }

      /* Responsive Design Enhancements */
      @media (max-width: 768px) {
        .container { padding: 8px; }
        header { padding: 12px; flex-direction: column; gap: 8px; }
        .title { font-size: 16px; }
        .controls { width: 100%; justify-content: space-between; }
        #settingsBtn { font-size: 16px; padding: 6px 8px; }
        .panel { border-radius: 10px; }
        .section-title { padding: 10px 12px; font-size: 14px; }
        .panel-body { padding: 10px; }
        .btn { padding: 6px 10px; font-size: 14px; }
        .modal .content { width: 95vw; padding: 10px; }
        .row.two { grid-template-columns: 1fr; }
        .chips { gap: 4px; }
        .chip { font-size: 11px; padding: 3px 6px; }
      }

      @media (max-width: 480px) {
        .title span:first-child { font-size: 14px; }
        .badge { font-size: 10px; padding: 1px 6px; }
        #dropZone { padding: 12px; }
        .cards { grid-template-columns: 1fr; }
        .progress-wrap { flex-direction: column; gap: 8px; }
        .progress { width: 100%; }
      }
    </style>
  </head>
  <body data-theme="Ocean">
    <header>
      <div class="title">
        <span style="font-size:20px">🏢 LawVriks ATS Analyser Pro</span>
        <span class="badge">Web UI</span>
      </div>
      <div class="controls">
        <label for="themeSelect" class="kbd">🎨 Theme</label>
        <select id="themeSelect" aria-label="Theme select">
          <option>Forest</option>
          <option>Sacred</option>
          <option selected>Ocean</option>
        </select>
        <button class="btn ghost" id="settingsBtn" aria-label="Open Settings" title="Settings">
          ⚙️
        </button>
      </div>
    </header>

    <div class="container">
      <div class="grid">
        <!-- Main: Upload + Roles + Analysis -->
        <section class="panel" aria-labelledby="workbench-title">
          <div class="section-title" id="workbench-title">🚀 Workbench</div>
          <div class="panel-body">
            <!-- Role Configuration -->
            <div class="panel" style="margin-bottom:12px" aria-labelledby="role-title">
              <div class="section-title" id="role-title">👤 Role Configuration</div>
              <div class="panel-body">
                <div class="row two">
                  <div>
                    <label>🎯 Role</label>
                    <select id="selectedRole"></select>
                  </div>
                  <div style="display:flex; gap:8px; align-items:end">
                    <button class="btn primary" id="saveRoleUpdatesBtn">💾 Save Updates</button>
                    <button class="btn ghost" id="resetRoleBtn">🔄 Reset to Preset</button>
                  </div>
                </div>
                <div class="row" style="margin-top:8px">
                  <label>📝 Job Description</label>
                  <textarea id="roleDescription" placeholder="Enter detailed job description..." style="width:100%; min-height:100px; background:var(--card); color:var(--text); border:1px solid var(--border); border-radius:10px; padding:10px"></textarea>
                </div>
                <div class="row" style="margin-top:8px">
                  <label>🏷️ Manual Keywords</label>
                  <input id="roleKeywords" type="text" placeholder="Enter keywords separated by commas (e.g., python, react, aws)" />
                </div>
              </div>
            </div>

            <!-- Uploads -->
            <div class="panel" style="margin-bottom:12px" aria-labelledby="upload-title">
              <div class="section-title" id="upload-title">📁 Upload Resumes</div>
              <div class="panel-body">
                <div class="row two">
                  <div>
                    <label for="fileInput">📄 Files (PDF/DOCX)</label>
                    <input id="fileInput" type="file" multiple accept=".pdf,.doc,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword" />
                  </div>
                  <div>
                    <label for="folderInput">📂 Folder (batch)</label>
                    <input id="folderInput" type="file" webkitdirectory directory />
                  </div>
                </div>
                <div id="dropZone" style="margin-top:10px; padding:20px; border:2px dashed var(--border); border-radius:12px; text-align:center; color:var(--muted); background: var(--card); transition: all 0.3s ease;">
                  <div style="font-size: 24px; margin-bottom: 8px;">📤</div>
                  <div>Drag & Drop files here or use the pickers above</div>
                  <div style="font-size: 12px; margin-top: 4px; opacity: 0.7;">Supports PDF and DOCX files</div>
                </div>
                <div id="uploadPreview" class="chips" style="margin-top:10px"></div>
              </div>
            </div>

            

            

            <!-- Analysis Controls -->
            <div class="panel" aria-labelledby="analysis-title">
              <div class="section-title" id="analysis-title">🔬 Analysis</div>
              <div class="panel-body">
                <div class="row two">
                  <div>
                    <label>⚡ Flow</label>
                    <input type="text" value="Resume Analysis" disabled />
                  </div>
                  <div></div>
                </div>
                <div class="progress-wrap" style="margin-top:12px">
                  <div class="progress" aria-label="Progress"><div id="progressBar"></div></div>
                  <span id="progressText" class="kbd">0%</span>
                </div>
                <div style="margin-top:12px; display:flex; gap:8px; flex-wrap:wrap">
                  <button class="btn primary" id="runBtn">▶️ Run Resume Analysis</button>
                  <button class="btn warning" id="stopBtn" style="display:none">⏹️ Stop Analysis</button>
                  <button class="btn ghost" id="resetBtn">🔄 Reset Results</button>
                  <button class="btn" id="exportCsvBtn">📊 Export Ranked CSV</button>
                </div>
              </div>
            </div>

          </div>
        </section>
      </div>

      <!-- Results -->
      <section class="panel" style="margin-top:16px" aria-labelledby="results-title">
        <div class="section-title" id="results-title">📈 Results</div>
        <div class="panel-body">
          <div style="display:flex; align-items:center; justify-content:space-between; gap:12px; margin-bottom:8px">
            <div class="chips">
              <span class="chip" id="resultCount">📊 0 results</span>
              <span class="chip">🏆 Final Ranking</span>
            </div>
            <div class="chips">
              <span class="chip">📊 ATS Score</span>
              <span class="chip">🎯 Skill Match</span>
              <span class="chip">✅ Analysis Complete</span>
            </div>
          </div>
          <div id="rankCards" class="cards" style="margin: 4px 0 12px 0"></div>

          <!-- Details Modal -->
          <div class="modal-backdrop" id="detailsBackdrop"></div>
          <div class="modal" id="detailsModal">
            <div class="content">
              <div class="header">
                <strong id="detailsTitle">Candidate Details</strong>
                <button class="btn" id="detailsCloseBtn">Close</button>
              </div>
              <div class="grid-two">
                <div>
                  <div class="chips" style="margin-bottom:8px">
                    <span class="chip" id="detailsEmail">email</span>
                    <span class="chip" id="detailsPhone">phone</span>
                  </div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Summary</div>
                    <div class="panel-body" id="detailsSummary"></div>
                  </div>
                  <div class="panel">
                    <div class="section-title">Potential</div>
                    <div class="panel-body" id="detailsPotential"></div>
                  </div>
                </div>
                <div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Found Keywords</div>
                    <div class="panel-body" id="detailsFoundKeywords"></div>
                  </div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Missing Keywords</div>
                    <div class="panel-body" id="detailsMissingKeywords"></div>
                  </div>
                  <div class="panel">
                    <div class="section-title">Alternative Roles</div>
                    <div class="panel-body" id="detailsAltRoles"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
        </div>
      </section>
    </div>

    <!-- Configuration Modal - Positioned outside all containers for proper overlay -->
    <div class="modal-backdrop" id="configBackdrop"></div>
    <div class="modal" id="configModal">
      <div class="content">
        <div class="header">
          <strong>⚙️ Configuration Settings</strong>
          <button class="btn" id="configCloseBtn">✕ Close</button>
        </div>
        <div class="panel-body">
          <div class="row two">
            <div>
              <label>🤖 Provider</label>
              <input type="text" value="Groq (Cloud)" disabled class="" />
            </div>
            <div>
              <label>🧠 Model</label>
              <select id="modalModelSelect"></select>
            </div>
          </div>
          <div class="row" id="modalApiKeyRow" style="margin-top:8px;">
            <div>
              <label>🔑 API Key</label>
              <input id="modalApiKeyInput" type="password" placeholder="Enter cloud API key" autocomplete="off" />
            </div>
          </div>
          <div class="chips" style="margin-top:10px">
            <span class="chip">🔒 Groq provider locked</span>
            <span class="chip">🛡️ Your key stays in-browser</span>
          </div>
        </div>
      </div>
    </div>

    <script>
      const state = {
        theme: 'Forest',
        provider: 'groq',
        models: {
          groq: [
            'openai/gpt-oss-120b',
            'moonshotai/kimi-k2-instruct',
            'compound-beta',
            'openai/gpt-oss-20b',
            'llama-3.3-70b-versatile',
            'meta-llama/llama-guard-4-12b',
            'deepseek-r1-distill-llama-70b',
            'qwen/qwen3-32b'
          ]
        },
        apiKey: '',
        roles: [],
        rolePresets: [
          { title: 'GenAI Engineer', description: 'Design, evaluate, and deploy LLM/GPT applications; prompt engineering; RAG; vector search; guardrails; observability.', keywords: ['python','langchain','groq','openai','rag','vector db','weaviate','qdrant','embeddings','prompt engineering','guardrails'] },
          { title: 'Backend Developer', description: 'Build scalable APIs and services; databases; security; performance; CI/CD; cloud native.', keywords: ['python','fastapi','django','rest','graphql','postgresql','redis','docker','kubernetes','aws','gcp','ci/cd'] },
          { title: 'Frontend Developer', description: 'Implement modern web UIs; state management; performance; accessibility; testing; UX.', keywords: ['javascript','react','typescript','vite','redux','zustand','tailwind','css','testing'] }
        ],
        results: [],
        sort: { key: 'atsScore', dir: 'desc' },
        running: false,
        progress: 0
      };

      // Theme
      const themeSelect = document.getElementById('themeSelect');
      const applyTheme = (name) => { document.body.setAttribute('data-theme', name); state.theme = name; localStorage.setItem('theme', name); }
      themeSelect.addEventListener('change', (e) => applyTheme(e.target.value));
      const storedTheme = localStorage.getItem('theme'); if (storedTheme) { themeSelect.value = storedTheme; applyTheme(storedTheme); }

      // Enhanced Configuration Modal
      const configBackdrop = document.getElementById('configBackdrop');
      const configModal = document.getElementById('configModal');
      const configCloseBtn = document.getElementById('configCloseBtn');
      const settingsBtn = document.getElementById('settingsBtn');
      const modalModelSelect = document.getElementById('modalModelSelect');
      const modalApiKeyInput = document.getElementById('modalApiKeyInput');

      function openConfigModal() {
        // Sync modal inputs with current state
        modalModelSelect.innerHTML = '';
        state.models.groq.forEach(m => {
          const opt = document.createElement('option');
          opt.value = m; opt.textContent = m;
          if (m === state.model) opt.selected = true;
          modalModelSelect.appendChild(opt);
        });
        modalApiKeyInput.value = state.apiKey || '';

        // Show modal with animation
        configBackdrop.style.display = 'block';
        configModal.style.display = 'flex';

        // Force reflow for animation
        configBackdrop.offsetHeight;
        configModal.offsetHeight;

        // Add show classes for animation
        configBackdrop.classList.add('show');
        configModal.classList.add('show');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus first input
        setTimeout(() => {
          if (modalApiKeyInput.value === '') {
            modalApiKeyInput.focus();
          }
        }, 300);
      }

      function closeConfigModal() {
        // Save changes from modal to state
        if (modalModelSelect.value) {
          state.model = modalModelSelect.value;
        }
        if (modalApiKeyInput.value !== undefined) {
          state.apiKey = modalApiKeyInput.value;
        }

        // Remove show classes for animation
        configBackdrop.classList.remove('show');
        configModal.classList.remove('show');

        // Hide modal after animation
        setTimeout(() => {
          configBackdrop.style.display = 'none';
          configModal.style.display = 'none';
          document.body.style.overflow = '';
        }, 300);
      }

      // Event listeners
      settingsBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        openConfigModal();
      });

      configCloseBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeConfigModal();
      });

      configBackdrop.addEventListener('click', (e) => {
        if (e.target === configBackdrop) {
          closeConfigModal();
        }
      });

      // Prevent modal content clicks from closing modal
      configModal.querySelector('.content').addEventListener('click', (e) => {
        e.stopPropagation();
      });

      // Real-time state updates
      modalModelSelect.addEventListener('change', () => {
        state.model = modalModelSelect.value;
      });

      modalApiKeyInput.addEventListener('input', (e) => {
        state.apiKey = e.target.value;
      });

      // Keyboard support
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && configModal.classList.contains('show')) {
          closeConfigModal();
        }
      });

      // Initialize model selection
      function refreshModels() {
        const provider = 'groq';
        state.provider = provider;
      }
      refreshModels();
      state.model = state.models.groq[0];
      // No flip: Role/JD lives beneath AI config

      // Uploads
      const fileInput = document.getElementById('fileInput');
      const folderInput = document.getElementById('folderInput');
      const uploadPreview = document.getElementById('uploadPreview');
      function renderUploads(files) {
        uploadPreview.innerHTML = '';
        Array.from(files).slice(0, 20).forEach(f => {
          const span = document.createElement('span');
          span.className = 'chip';
          span.textContent = f.webkitRelativePath || f.name;
          uploadPreview.appendChild(span);
        });
        if (files.length > 20) {
          const more = document.createElement('span'); more.className='chip'; more.textContent = `+${files.length - 20} more`; uploadPreview.appendChild(more);
        }
      }
      fileInput.addEventListener('change', () => renderUploads(fileInput.files));
      folderInput.addEventListener('change', () => renderUploads(folderInput.files));
      const dropZone = document.getElementById('dropZone');
      ;['dragenter','dragover'].forEach(ev => dropZone.addEventListener(ev, e => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
      }));
      ;['dragleave','drop'].forEach(ev => dropZone.addEventListener(ev, e => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
      }));
      dropZone.addEventListener('drop', (e) => {
        const files = e.dataTransfer.files;
        renderUploads(files);
      });

      // Roles (presets with save/reset)
      const roleDescription = document.getElementById('roleDescription');
      const roleKeywords = document.getElementById('roleKeywords');
      const selectedRole = document.getElementById('selectedRole');
      const resetRoleBtn = document.getElementById('resetRoleBtn');
      const saveRoleUpdatesBtn = document.getElementById('saveRoleUpdatesBtn');

      function persistRoles() { localStorage.setItem('roles', JSON.stringify(state.roles)); }
      function loadRoles() { try { state.roles = JSON.parse(localStorage.getItem('roles')||'[]'); } catch { state.roles = []; } }
      function renderRoleOptions() {
        selectedRole.innerHTML = '';
        state.roles.forEach((r, idx) => {
          const opt = document.createElement('option'); opt.value = idx; opt.textContent = r.title; selectedRole.appendChild(opt);
        });
      }
      function ensureRoles() {
        if (!state.roles || state.roles.length === 0) {
          state.roles = JSON.parse(JSON.stringify(state.rolePresets));
        }
      }
      function applyRoleToEditors(idx) {
        const role = state.roles[idx];
        if (!role) return;
        roleDescription.value = role.description || '';
        roleKeywords.value = (role.keywords||[]).join(', ');
      }
      loadRoles(); ensureRoles(); renderRoleOptions(); applyRoleToEditors(0);
      selectedRole.addEventListener('change', () => applyRoleToEditors(parseInt(selectedRole.value,10)));
      resetRoleBtn.addEventListener('click', () => {
        const idx = parseInt(selectedRole.value,10) || 0;
        const preset = state.rolePresets.find(p => p.title === state.roles[idx]?.title);
        if (preset) {
          roleDescription.value = preset.description;
          roleKeywords.value = preset.keywords.join(', ');
        }
      });
      saveRoleUpdatesBtn.addEventListener('click', () => {
        const idx = parseInt(selectedRole.value,10) || 0;
        const role = state.roles[idx]; if (!role) return;
        role.description = roleDescription.value.trim();
        role.keywords = roleKeywords.value.split(',').map(s=>s.trim()).filter(Boolean);
        persistRoles();
      });

      // Analysis
      const progressBar = document.getElementById('progressBar');
      const progressText = document.getElementById('progressText');
      const runBtn = document.getElementById('runBtn');
      const resetBtn = document.getElementById('resetBtn');
      // resume-only flow

      let simTimer = null;
      const filenameToFile = new Map();
      function setProgress(p) {
        state.progress = Math.max(0, Math.min(100, p));
        progressBar.style.width = state.progress + '%';
        progressText.textContent = Math.round(state.progress) + '%';
      }
      function setRunning(running) { 
        state.running = running; 
        runBtn.disabled = running;
        document.getElementById('stopBtn').style.display = running ? 'inline-block' : 'none';
      }
      async function buildFormData() {
        const form = new FormData();
        
        // Collect files
        filenameToFile.clear();
        const files = [];
        Array.from(fileInput.files || []).forEach(f => { files.push(f); filenameToFile.set(f.name, f); });
        Array.from(folderInput.files || []).forEach(f => { files.push(f); filenameToFile.set(f.name, f); });
        
        // Validation
        if (!files.length) throw new Error('Please add at least one resume file.');
        if (!roleDescription.value.trim()) throw new Error('Please enter a job description.');
        if (!state.apiKey.trim()) throw new Error('Please enter your Groq API key.');
        
        // Add files
        files.forEach(f => form.append('resumes', f, f.name));
        
        // Add form data
        form.append('job_description', roleDescription.value.trim());
        form.append('manual_keywords', roleKeywords.value.trim());
        
        // Selected role
        const selectedRoleIndex = parseInt(selectedRole.value, 10) || 0;
        const selectedRoleTitle = state.roles[selectedRoleIndex]?.title || 'GenAI Engineer';
        form.append('selected_role', selectedRoleTitle);
        
        // AI config
        form.append('groq_model', modelSelect.value || 'llama-3.3-70b-versatile');
        form.append('groq_api_key', state.apiKey.trim());
        
        return form;
      }

      async function startAnalysis() {
        const form = await buildFormData();
        const resp = await fetch('/analyze', { method: 'POST', body: form });
        if (!resp.ok) {
          const errorText = await resp.text();
          throw new Error(`Failed to start analysis: ${errorText}`);
        }
      }

      async function pollProgressLoop() {
        const poll = async () => {
          const r = await fetch('/progress');
          if (!r.ok) throw new Error('Progress fetch failed');
          const data = await r.json();
          const { current, total, completed, results, current_file } = data;
          const pct = total > 0 ? Math.min(100, (current/total) * 100) : 0;
          
          setProgress(completed ? 100 : pct);
          
          // Update current file status
          if (current_file) {
            progressText.textContent = completed ? '100% - Complete' : `${Math.round(pct)}% - ${current_file}`;
          }
          
          // Map results (only show final ranking when complete)
          if (results && results.length > 0) {
            const mapped = results.map(res => {
              const a = res.analysis || {};
              const c = a.contact_info || {};
              return {
                candidate: c.name || 'N/A',
                phone: c.phone || '',
                email: c.email || '',
                role: res.analysis?.selected_role || selectedRole.options[selectedRole.selectedIndex]?.text || 'GenAI Engineer',
                atsScore: a.ats_score ?? 0,
                skillMatch: a.researcher_potential_score ?? 0,
                file: res.filename || '',
                analysis: a
              };
            });
            state.results = mapped;
            renderResults();
          }
          
          if (!completed && state.running) {
            simTimer = setTimeout(poll, 1000);
          } else {
            setRunning(false);
          }
        };
        await poll();
      }

      runBtn.addEventListener('click', async () => {
        try {
          setRunning(true); setProgress(0);
          state.results = []; renderResults();
          await startAnalysis();
          await pollProgressLoop();
        } catch (e) {
          setRunning(false);
          alert((e && e.message) || 'Failed to run analysis');
        }
      });
      resetBtn.addEventListener('click', async () => { 
        try { 
          await fetch('/reset-results', { method: 'POST' }); 
        } catch {} 
        setRunning(false); 
        clearTimeout(simTimer); 
        setProgress(0); 
        state.results = []; 
        renderResults(); 
        progressText.textContent = '0%';
      });
      
      document.getElementById('stopBtn').addEventListener('click', async () => {
        try {
          await fetch('/stop-analysis', { method: 'POST' });
          setRunning(false);
          clearTimeout(simTimer);
        } catch {}
      });

      // Results
      const resultCount = document.getElementById('resultCount');
      const rankCards = document.getElementById('rankCards');
      function sortResults(key) {
        const dir = (state.sort.key === key && state.sort.dir === 'asc') ? 'desc' : 'asc';
        state.sort = { key, dir };
        state.results.sort((a,b) => {
          const av = a[key]; const bv = b[key];
          if (typeof av === 'number' && typeof bv === 'number') return dir==='asc' ? av-bv : bv-av;
          return dir==='asc' ? (''+av).localeCompare(''+bv) : (''+bv).localeCompare(''+av);
        });
        renderResults();
      }
      function renderResults() {
        resultCount.textContent = `${state.results.length} results`;
        // vertical ranking cards: name + circular ATS + details/preview
        rankCards.innerHTML = state.results
          .slice()
          .sort((a,b) => b.atsScore - a.atsScore)
          .map((r, i) => `
            <div class="card">
              <div class="card-inner">
                <div class="card-face front" style="gap:10px; align-items:center;">
                  <div style="display:flex; align-items:center; justify-content:space-between; width:100%">
                    <div style="display:flex; align-items:center; gap:8px">
                      <span class="badge">#${i+1}</span>
                      <strong>${r.candidate || 'Unknown'}</strong>
                    </div>
                    <div class="circle" style="--val:${r.atsScore}"><span>${r.atsScore}%</span></div>
                  </div>
                  <div style="display:flex; gap:8px">
                    <button class="btn" data-details-index="${i}">Details</button>
                    <button class="btn" data-preview-fn="${encodeURIComponent(r.file||'')}">View Resume</button>
                    <button class="btn" data-copy-index="${i}">Copy</button>
                  </div>
                </div>
              </div>
            </div>`).join('');

        // copy + preview + details handlers
        rankCards.querySelectorAll('button[data-copy-index]').forEach(btn => btn.addEventListener('click', (e) => {
          const i = parseInt(e.currentTarget.getAttribute('data-copy-index'), 10);
          const row = state.results[i]; if (!row) return;
          const text = `Name: ${row.candidate || ''}\nMobile: ${row.phone || ''}\nEmail: ${row.email || ''}`;
          navigator.clipboard.writeText(text).catch(()=>{});
        }));
        rankCards.querySelectorAll('button[data-details-index]').forEach(btn => btn.addEventListener('click', (e) => {
          const i = parseInt(e.currentTarget.getAttribute('data-details-index'), 10);
          const row = state.results[i]; if (!row) return;
          openDetails(row);
        }));
        rankCards.querySelectorAll('button[data-preview-fn]').forEach(btn => btn.addEventListener('click', (e) => {
          const fn = decodeURIComponent(e.currentTarget.getAttribute('data-preview-fn')||'');
          if (!fn) return;
          const f = filenameToFile.get(fn);
          if (f) {
            const url = URL.createObjectURL(f);
            window.open(url, '_blank');
            setTimeout(()=>URL.revokeObjectURL(url), 5000);
          } else {
            alert('Preview unavailable: file not found from upload list.');
          }
        }));

        // table removed per design; only cards are shown
      }

      // Export
      document.getElementById('exportCsvBtn').addEventListener('click', () => {
        if (!state.results.length) return;
        const headers = ['candidate','role','atsScore','skillMatch','file'];
        const csv = [headers.join(',')].concat(state.results.map(r => headers.map(h => JSON.stringify(r[h]??'')).join(','))).join('\n');
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a'); a.href = url; a.download = 'ats_results.csv'; a.click(); URL.revokeObjectURL(url);
      });
      // Details modal controls
      const detailsBackdrop = document.getElementById('detailsBackdrop');
      const detailsModal = document.getElementById('detailsModal');
      const detailsCloseBtn = document.getElementById('detailsCloseBtn');
      function openDetails(row) {
        document.getElementById('detailsTitle').textContent = row.candidate || 'Candidate';
        document.getElementById('detailsEmail').textContent = row.email || 'no-email';
        document.getElementById('detailsPhone').textContent = row.phone || 'no-phone';
        const a = row.analysis || {};
        document.getElementById('detailsSummary').innerHTML = `
          <div class="chips" style="margin-bottom:8px">
            <span class="chip">ATS ${a.ats_score ?? 0}</span>
            <span class="chip">Research ${a.researcher_potential_score ?? 0}</span>
          </div>
          <div><strong>Eligibility:</strong><br/>${(a.eligibility_for_role||'').toString()}</div>`;
        document.getElementById('detailsPotential').innerHTML = `
          <div><strong>Expected Potential:</strong><br/>${(a.expected_potential||'').toString()}</div>`;
        const found = (a.found_keywords||[]).map(k=>`<span class='chip'>${k}</span>`).join(' ');
        const missing = (a.missing_keywords||[]).map(k=>`<span class='chip'>${k}</span>`).join(' ');
        document.getElementById('detailsFoundKeywords').innerHTML = found || '<span class="chip">None</span>';
        document.getElementById('detailsMissingKeywords').innerHTML = missing || '<span class="chip">None</span>';
        const alts = (a.alternative_job_roles||[]).map(o=>`<span class='chip'>${o.role}: ${o.score}</span>`).join(' ');
        document.getElementById('detailsAltRoles').innerHTML = alts || '<span class="chip">None</span>';
        detailsBackdrop.style.display = 'block';
        detailsModal.style.display = 'flex';
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
      function closeDetails(){ detailsBackdrop.style.display = 'none'; detailsModal.style.display = 'none'; }
      detailsCloseBtn.addEventListener('click', closeDetails);
      detailsBackdrop.addEventListener('click', closeDetails);
      // JSON export removed per request
    </script>
  </body>
  </html>


