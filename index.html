<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>LawVriks ATS Analyser Pro</title>
    <style>
      :root {
        /* Grassy Forest: traditional, professional */
        --bg: #0c120d;            /* deep forest */
        --panel: rgba(17, 28, 20, 0.65);
        --text: #e9f5ea;
        --muted: #b6c9b8;
        --primary: #5fbf5a;       /* green */
        --primary-600: #45a049;   /* deeper green */
        --accent: #ffb347;        /* warm saffron/gold */
        --danger: #e57373;
        --warning: #f6c453;      /* turmeric */
        --border: rgba(150, 200, 160, 0.28);
        --card: rgba(20, 33, 24, 0.55);
        --chip: rgba(24, 40, 28, 0.65);
        --shadow: 0 22px 44px rgba(0,0,0,.48);
        --blur: saturate(160%) blur(10px);
        --grad: linear-gradient(135deg, rgba(95,191,90,.18), rgba(255,179,71,.12));
      }

      /* Themes */
      [data-theme="Forest"] {
        --bg: #0b1210;
        --panel: #0f1a16;
        --card: #12201a;
        --primary: #84f3c6;
        --primary-600: #3fe0a4;
        --accent: #7cc5ff;
      }
      [data-theme="Sacred"] {
        --bg: #130d1a;
        --panel: #1a1324;
        --card: #1e172a;
        --primary: #d6b3ff;
        --primary-600: #bf91ff;
        --accent: #81e6d9;
      }
      [data-theme="Ocean"] {
        --bg: #0a1018;
        --panel: #0f1725;
        --card: #101b2d;
        --primary: #7dd3fc;
        --primary-600: #38bdf8;
        --accent: #a7f3d0;
      }

      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        font-family: ui-sans-serif, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
        background: var(--bg);
        color: var(--text);
      }
      a { color: var(--accent); text-decoration: none; }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 16px;
      }
      header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        padding: 16px;
        background: var(--panel);
        backdrop-filter: var(--blur);
        -webkit-backdrop-filter: var(--blur);
        border-bottom: 1px solid var(--border);
        position: sticky;
        top: 0;
        z-index: 50;
      }
      header .title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 700;
        letter-spacing: .3px;
      }
      .badge {
        background: linear-gradient(135deg, var(--primary), var(--accent));
        color: #0b0f14;
        padding: 2px 8px;
        border-radius: 999px;
        font-size: 12px;
        font-weight: 700;
      }
      .controls { display: flex; gap: 12px; align-items: center; }
      select, input[type="text"], input[type="password"], input[type="file"], input[type="email"], input[type="tel"], textarea {
        background: var(--card);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 8px 10px;
        border-radius: 10px;
      }
      .grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
      }
      @media (min-width: 980px) {
        .grid { grid-template-columns: 350px 1fr; }
      }
      .panel {
        background: var(--panel);
        border: 1px solid var(--border);
        border-radius: 14px;
        box-shadow: var(--shadow);
        backdrop-filter: var(--blur);
        -webkit-backdrop-filter: var(--blur);
        background-image: var(--grad);
      }
      .section-title {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border);
        font-weight: 700;
      }
      .panel-body { padding: 14px; }

      .row { display: grid; grid-template-columns: 1fr; gap: 12px; }
      @media (min-width: 640px) { .row.two { grid-template-columns: 1fr 1fr; } }

      .btn {
        background: var(--card);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 8px 12px;
        border-radius: 10px;
        cursor: pointer;
      }
      .btn.primary { background: var(--primary-600); color: #07120f; border-color: transparent; font-weight: 700; box-shadow: 0 8px 22px rgba(68,226,172,.25); }
      .btn.ghost { background: transparent; }
      .btn.warning { background: var(--warning); color: #111; border-color: transparent; }
      .btn.danger { background: var(--danger); color: #111; border-color: transparent; }
      .btn:disabled { opacity: .6; cursor: not-allowed; }

      .chips { display: flex; flex-wrap: wrap; gap: 8px; }
      .chip {
        background: var(--chip);
        border: 1px solid var(--border);
        color: var(--muted);
        padding: 4px 8px;
        border-radius: 999px;
        font-size: 12px;
      }

      /* Flip cards for Role Management */
      .cards { display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 12px; }
      /* (removed flip views) */
      .card {
        perspective: 1000px;
      }
      .card-inner {
        position: relative;
        width: 100%;
        height: 180px;
        transform-style: preserve-3d;
        transition: transform .5s ease;
      }
      .card.flipped .card-inner { transform: rotateY(180deg); }
      .card-face {
        position: absolute; inset: 0;
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 12px;
        backface-visibility: hidden;
        display: flex; flex-direction: column; gap: 8px;
      }
      .card-face.back { transform: rotateY(180deg); }

      /* Progress */
      .progress-wrap { display: flex; align-items: center; gap: 12px; }
      .progress {
        flex: 1;
        height: 10px;
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 999px;
        overflow: hidden;
      }
      .progress > div { height: 100%; width: 0%; background: linear-gradient(90deg, var(--primary), var(--accent)); transition: width .3s ease; }

      /* Table */
      table { width: 100%; border-collapse: collapse; }
      th, td { border-bottom: 1px solid var(--border); padding: 10px; text-align: left; }
      th { color: var(--muted); cursor: pointer; }
      tbody tr:hover { background: rgba(255,255,255,.03); }

      .kbd { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; background: var(--card); border: 1px solid var(--border); padding: 2px 6px; border-radius: 6px; color: var(--muted); font-size: 12px; }

      /* Circular score */
      .circle {
        width: 64px; height: 64px; border-radius: 50%;
        background: conic-gradient(var(--primary) calc(var(--val)*1%), rgba(255,255,255,0.08) 0);
        display: grid; place-items: center;
        border: 1px solid var(--border);
      }
      .circle span { font-weight: 700; font-size: 14px; }

      /* Modal */
      .modal-backdrop { position: fixed; inset: 0; background: rgba(0,0,0,.6); display: none; z-index: 100; }
      .modal { position: fixed; inset: 0; display: none; align-items: center; justify-content: center; z-index: 101; }
      .modal .content { width: min(860px, 92vw); max-height: 85vh; overflow: auto; background: var(--panel); border: 1px solid var(--border); border-radius: 14px; box-shadow: var(--shadow); padding: 14px; backdrop-filter: var(--blur); }
      .modal .header { display:flex; align-items:center; justify-content: space-between; margin-bottom: 8px; }
      .grid-two { display:grid; grid-template-columns: 1fr; gap: 10px; }
      @media (min-width: 900px) { .grid-two { grid-template-columns: 1fr 1fr; } }
    </style>
  </head>
  <body data-theme="Forest">
    <header>
      <div class="title">
        <span style="font-size:20px">LawVriks ATS Analyser Pro</span>
        <span class="badge">Web UI</span>
      </div>
      <div class="controls">
        <label for="themeSelect" class="kbd">Theme</label>
        <select id="themeSelect" aria-label="Theme select">
          <option>Forest</option>
          <option>Sacred</option>
          <option>Ocean</option>
        </select>
      </div>
    </header>

    <div class="container">
      <div class="grid">
        <!-- Left: Configuration -->
        <section class="panel" aria-labelledby="ai-config-title">
          <div class="section-title" id="ai-config-title">Configuration</div>
          <div class="panel-body">
            <div class="row two">
              <div>
                <label>Provider</label>
                <input type="text" value="Groq (Cloud)" disabled class="" />
              </div>
              <div>
                <label>Model</label>
                <select id="modelSelect"></select>
              </div>
            </div>
            <div class="row" id="apiKeyRow" style="margin-top:8px;">
              <div>
                <label>API Key</label>
                <input id="apiKeyInput" type="password" placeholder="Enter cloud API key" autocomplete="off" />
              </div>
            </div>
            <div class="chips" style="margin-top:10px">
              <span class="chip">Groq provider locked</span>
              <span class="chip">Your key stays in-browser</span>
            </div>
            <hr style="border: none; border-top: 1px solid var(--border); margin: 12px 0" />
            <div class="row two">
              <div>
                <label>Role</label>
                <select id="selectedRole"></select>
              </div>
              <div style="display:flex; gap:8px; align-items:end">
                <button class="btn primary" id="saveRoleUpdatesBtn">Save Updates</button>
                <button class="btn ghost" id="resetRoleBtn">Reset to Preset</button>
              </div>
            </div>
            <div class="row" style="margin-top:8px">
              <textarea id="roleDescription" placeholder="Job description" style="width:100%; min-height:100px; background:var(--card); color:var(--text); border:1px solid var(--border); border-radius:10px; padding:10px"></textarea>
            </div>
            <div class="row" style="margin-top:8px">
              <input id="roleKeywords" type="text" placeholder="Manual keywords (comma separated)" />
            </div>
          </div>
        </section>

        <!-- Right: Upload + Roles + Analysis -->
        <section class="panel" aria-labelledby="workbench-title">
          <div class="section-title" id="workbench-title">Workbench</div>
          <div class="panel-body">
            <!-- Uploads -->
            <div class="panel" style="margin-bottom:12px" aria-labelledby="upload-title">
              <div class="section-title" id="upload-title">Upload Resumes</div>
              <div class="panel-body">
                <div class="row two">
                  <div>
                    <label for="fileInput">Files (PDF/DOCX)</label>
                    <input id="fileInput" type="file" multiple accept=".pdf,.doc,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword" />
                  </div>
                  <div>
                    <label for="folderInput">Folder (batch)</label>
                    <input id="folderInput" type="file" webkitdirectory directory />
                  </div>
                </div>
                <div id="dropZone" style="margin-top:10px; padding:16px; border:2px dashed var(--border); border-radius:12px; text-align:center; color:var(--muted)">Drag & Drop files here or use the pickers above</div>
                <div id="uploadPreview" class="chips" style="margin-top:10px"></div>
              </div>
            </div>

            

            

            <!-- Analysis Controls -->
            <div class="panel" aria-labelledby="analysis-title">
              <div class="section-title" id="analysis-title">Analysis</div>
              <div class="panel-body">
                <div class="row two">
                  <div>
                    <label>Flow</label>
                    <input type="text" value="Resume Analysis" disabled />
                  </div>
                  <div></div>
                </div>
                <div class="progress-wrap" style="margin-top:12px">
                  <div class="progress" aria-label="Progress"><div id="progressBar"></div></div>
                  <span id="progressText" class="kbd">0%</span>
                </div>
                <div style="margin-top:12px; display:flex; gap:8px; flex-wrap:wrap">
                  <button class="btn primary" id="runBtn">Run Resume Analysis</button>
                  <button class="btn warning" id="stopBtn" style="display:none">Stop Analysis</button>
                  <button class="btn ghost" id="resetBtn">Reset Results</button>
                  <button class="btn" id="exportCsvBtn">Export Ranked CSV</button>
                </div>
              </div>
            </div>

          </div>
        </section>
      </div>

      <!-- Results -->
      <section class="panel" style="margin-top:16px" aria-labelledby="results-title">
        <div class="section-title" id="results-title">Results</div>
        <div class="panel-body">
          <div style="display:flex; align-items:center; justify-content:space-between; gap:12px; margin-bottom:8px">
            <div class="chips">
              <span class="chip" id="resultCount">0 results</span>
              <span class="chip">🏆 Final Ranking</span>
            </div>
            <div class="chips">
              <span class="chip">📊 ATS Score</span>
              <span class="chip">🎯 Skill Match</span>
              <span class="chip">✅ Analysis Complete</span>
            </div>
          </div>
          <div id="rankCards" class="cards" style="margin: 4px 0 12px 0"></div>
          <div class="modal-backdrop" id="detailsBackdrop"></div>
          <div class="modal" id="detailsModal">
            <div class="content">
              <div class="header">
                <strong id="detailsTitle">Candidate Details</strong>
                <button class="btn" id="detailsCloseBtn">Close</button>
              </div>
              <div class="grid-two">
                <div>
                  <div class="chips" style="margin-bottom:8px">
                    <span class="chip" id="detailsEmail">email</span>
                    <span class="chip" id="detailsPhone">phone</span>
                  </div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Summary</div>
                    <div class="panel-body" id="detailsSummary"></div>
                  </div>
                  <div class="panel">
                    <div class="section-title">Potential</div>
                    <div class="panel-body" id="detailsPotential"></div>
                  </div>
                </div>
                <div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Found Keywords</div>
                    <div class="panel-body" id="detailsFoundKeywords"></div>
                  </div>
                  <div class="panel" style="margin-bottom:8px">
                    <div class="section-title">Missing Keywords</div>
                    <div class="panel-body" id="detailsMissingKeywords"></div>
                  </div>
                  <div class="panel">
                    <div class="section-title">Alternative Roles</div>
                    <div class="panel-body" id="detailsAltRoles"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
        </div>
      </section>
    </div>

    <script>
      const state = {
        theme: 'Forest',
        provider: 'groq',
        models: {
          groq: [
            'openai/gpt-oss-120b',
            'moonshotai/kimi-k2-instruct',
            'compound-beta',
            'openai/gpt-oss-20b',
            'llama-3.3-70b-versatile',
            'meta-llama/llama-guard-4-12b',
            'deepseek-r1-distill-llama-70b',
            'qwen/qwen3-32b'
          ]
        },
        apiKey: '',
        roles: [],
        rolePresets: [
          { title: 'GenAI Engineer', description: 'Design, evaluate, and deploy LLM/GPT applications; prompt engineering; RAG; vector search; guardrails; observability.', keywords: ['python','langchain','groq','openai','rag','vector db','weaviate','qdrant','embeddings','prompt engineering','guardrails'] },
          { title: 'Backend Developer', description: 'Build scalable APIs and services; databases; security; performance; CI/CD; cloud native.', keywords: ['python','fastapi','django','rest','graphql','postgresql','redis','docker','kubernetes','aws','gcp','ci/cd'] },
          { title: 'Frontend Developer', description: 'Implement modern web UIs; state management; performance; accessibility; testing; UX.', keywords: ['javascript','react','typescript','vite','redux','zustand','tailwind','css','testing'] }
        ],
        results: [],
        sort: { key: 'atsScore', dir: 'desc' },
        running: false,
        progress: 0
      };

      // Theme
      const themeSelect = document.getElementById('themeSelect');
      const applyTheme = (name) => { document.body.setAttribute('data-theme', name); state.theme = name; localStorage.setItem('theme', name); }
      themeSelect.addEventListener('change', (e) => applyTheme(e.target.value));
      const storedTheme = localStorage.getItem('theme'); if (storedTheme) { themeSelect.value = storedTheme; applyTheme(storedTheme); }

      // Provider & Model (Groq locked)
      const modelSelect = document.getElementById('modelSelect');
      const apiKeyRow = document.getElementById('apiKeyRow');
      const apiKeyInput = document.getElementById('apiKeyInput');
      function refreshModels() {
        const provider = 'groq';
        state.provider = provider;
        apiKeyRow.style.display = '';
        modelSelect.innerHTML = '';
        state.models[provider].forEach(m => {
          const opt = document.createElement('option');
          opt.value = m; opt.textContent = m; modelSelect.appendChild(opt);
        });
      }
      modelSelect.addEventListener('change', () => state.model = modelSelect.value);
      apiKeyInput.addEventListener('input', (e) => state.apiKey = e.target.value);
      refreshModels(); state.model = modelSelect.value;
      // No flip: Role/JD lives beneath AI config

      // Uploads
      const fileInput = document.getElementById('fileInput');
      const folderInput = document.getElementById('folderInput');
      const uploadPreview = document.getElementById('uploadPreview');
      function renderUploads(files) {
        uploadPreview.innerHTML = '';
        Array.from(files).slice(0, 20).forEach(f => {
          const span = document.createElement('span');
          span.className = 'chip';
          span.textContent = f.webkitRelativePath || f.name;
          uploadPreview.appendChild(span);
        });
        if (files.length > 20) {
          const more = document.createElement('span'); more.className='chip'; more.textContent = `+${files.length - 20} more`; uploadPreview.appendChild(more);
        }
      }
      fileInput.addEventListener('change', () => renderUploads(fileInput.files));
      folderInput.addEventListener('change', () => renderUploads(folderInput.files));
      const dropZone = document.getElementById('dropZone');
      ;['dragenter','dragover'].forEach(ev => dropZone.addEventListener(ev, e => { e.preventDefault(); dropZone.style.borderColor = 'var(--primary-600)'; }));
      ;['dragleave','drop'].forEach(ev => dropZone.addEventListener(ev, e => { e.preventDefault(); dropZone.style.borderColor = 'var(--border)'; }));
      dropZone.addEventListener('drop', (e) => { const files = e.dataTransfer.files; renderUploads(files); });

      // Roles (presets with save/reset)
      const roleDescription = document.getElementById('roleDescription');
      const roleKeywords = document.getElementById('roleKeywords');
      const selectedRole = document.getElementById('selectedRole');
      const resetRoleBtn = document.getElementById('resetRoleBtn');
      const saveRoleUpdatesBtn = document.getElementById('saveRoleUpdatesBtn');

      function persistRoles() { localStorage.setItem('roles', JSON.stringify(state.roles)); }
      function loadRoles() { try { state.roles = JSON.parse(localStorage.getItem('roles')||'[]'); } catch { state.roles = []; } }
      function renderRoleOptions() {
        selectedRole.innerHTML = '';
        state.roles.forEach((r, idx) => {
          const opt = document.createElement('option'); opt.value = idx; opt.textContent = r.title; selectedRole.appendChild(opt);
        });
      }
      function ensureRoles() {
        if (!state.roles || state.roles.length === 0) {
          state.roles = JSON.parse(JSON.stringify(state.rolePresets));
        }
      }
      function applyRoleToEditors(idx) {
        const role = state.roles[idx];
        if (!role) return;
        roleDescription.value = role.description || '';
        roleKeywords.value = (role.keywords||[]).join(', ');
      }
      loadRoles(); ensureRoles(); renderRoleOptions(); applyRoleToEditors(0);
      selectedRole.addEventListener('change', () => applyRoleToEditors(parseInt(selectedRole.value,10)));
      resetRoleBtn.addEventListener('click', () => {
        const idx = parseInt(selectedRole.value,10) || 0;
        const preset = state.rolePresets.find(p => p.title === state.roles[idx]?.title);
        if (preset) {
          roleDescription.value = preset.description;
          roleKeywords.value = preset.keywords.join(', ');
        }
      });
      saveRoleUpdatesBtn.addEventListener('click', () => {
        const idx = parseInt(selectedRole.value,10) || 0;
        const role = state.roles[idx]; if (!role) return;
        role.description = roleDescription.value.trim();
        role.keywords = roleKeywords.value.split(',').map(s=>s.trim()).filter(Boolean);
        persistRoles();
      });

      // Analysis
      const progressBar = document.getElementById('progressBar');
      const progressText = document.getElementById('progressText');
      const runBtn = document.getElementById('runBtn');
      const resetBtn = document.getElementById('resetBtn');
      // resume-only flow

      let simTimer = null;
      const filenameToFile = new Map();
      function setProgress(p) {
        state.progress = Math.max(0, Math.min(100, p));
        progressBar.style.width = state.progress + '%';
        progressText.textContent = Math.round(state.progress) + '%';
      }
      function setRunning(running) { 
        state.running = running; 
        runBtn.disabled = running;
        document.getElementById('stopBtn').style.display = running ? 'inline-block' : 'none';
      }
      async function buildFormData() {
        const form = new FormData();
        
        // Collect files
        filenameToFile.clear();
        const files = [];
        Array.from(fileInput.files || []).forEach(f => { files.push(f); filenameToFile.set(f.name, f); });
        Array.from(folderInput.files || []).forEach(f => { files.push(f); filenameToFile.set(f.name, f); });
        
        // Validation
        if (!files.length) throw new Error('Please add at least one resume file.');
        if (!roleDescription.value.trim()) throw new Error('Please enter a job description.');
        if (!state.apiKey.trim()) throw new Error('Please enter your Groq API key.');
        
        // Add files
        files.forEach(f => form.append('resumes', f, f.name));
        
        // Add form data
        form.append('job_description', roleDescription.value.trim());
        form.append('manual_keywords', roleKeywords.value.trim());
        
        // Selected role
        const selectedRoleIndex = parseInt(selectedRole.value, 10) || 0;
        const selectedRoleTitle = state.roles[selectedRoleIndex]?.title || 'GenAI Engineer';
        form.append('selected_role', selectedRoleTitle);
        
        // AI config
        form.append('groq_model', modelSelect.value || 'llama-3.3-70b-versatile');
        form.append('groq_api_key', state.apiKey.trim());
        
        return form;
      }

      async function startAnalysis() {
        const form = await buildFormData();
        const resp = await fetch('/analyze', { method: 'POST', body: form });
        if (!resp.ok) {
          const errorText = await resp.text();
          throw new Error(`Failed to start analysis: ${errorText}`);
        }
      }

      async function pollProgressLoop() {
        const poll = async () => {
          const r = await fetch('/progress');
          if (!r.ok) throw new Error('Progress fetch failed');
          const data = await r.json();
          const { current, total, completed, results, current_file } = data;
          const pct = total > 0 ? Math.min(100, (current/total) * 100) : 0;
          
          setProgress(completed ? 100 : pct);
          
          // Update current file status
          if (current_file) {
            progressText.textContent = completed ? '100% - Complete' : `${Math.round(pct)}% - ${current_file}`;
          }
          
          // Map results (only show final ranking when complete)
          if (results && results.length > 0) {
            const mapped = results.map(res => {
              const a = res.analysis || {};
              const c = a.contact_info || {};
              return {
                candidate: c.name || 'N/A',
                phone: c.phone || '',
                email: c.email || '',
                role: res.analysis?.selected_role || selectedRole.options[selectedRole.selectedIndex]?.text || 'GenAI Engineer',
                atsScore: a.ats_score ?? 0,
                skillMatch: a.researcher_potential_score ?? 0,
                file: res.filename || '',
                analysis: a
              };
            });
            state.results = mapped;
            renderResults();
          }
          
          if (!completed && state.running) {
            simTimer = setTimeout(poll, 1000);
          } else {
            setRunning(false);
          }
        };
        await poll();
      }

      runBtn.addEventListener('click', async () => {
        try {
          setRunning(true); setProgress(0);
          state.results = []; renderResults();
          await startAnalysis();
          await pollProgressLoop();
        } catch (e) {
          setRunning(false);
          alert((e && e.message) || 'Failed to run analysis');
        }
      });
      resetBtn.addEventListener('click', async () => { 
        try { 
          await fetch('/reset-results', { method: 'POST' }); 
        } catch {} 
        setRunning(false); 
        clearTimeout(simTimer); 
        setProgress(0); 
        state.results = []; 
        renderResults(); 
        progressText.textContent = '0%';
      });
      
      document.getElementById('stopBtn').addEventListener('click', async () => {
        try {
          await fetch('/stop-analysis', { method: 'POST' });
          setRunning(false);
          clearTimeout(simTimer);
        } catch {}
      });

      // Results
      const resultCount = document.getElementById('resultCount');
      const rankCards = document.getElementById('rankCards');
      function sortResults(key) {
        const dir = (state.sort.key === key && state.sort.dir === 'asc') ? 'desc' : 'asc';
        state.sort = { key, dir };
        state.results.sort((a,b) => {
          const av = a[key]; const bv = b[key];
          if (typeof av === 'number' && typeof bv === 'number') return dir==='asc' ? av-bv : bv-av;
          return dir==='asc' ? (''+av).localeCompare(''+bv) : (''+bv).localeCompare(''+av);
        });
        renderResults();
      }
      function renderResults() {
        resultCount.textContent = `${state.results.length} results`;
        // vertical ranking cards: name + circular ATS + details/preview
        rankCards.innerHTML = state.results
          .slice()
          .sort((a,b) => b.atsScore - a.atsScore)
          .map((r, i) => `
            <div class="card">
              <div class="card-inner">
                <div class="card-face front" style="gap:10px; align-items:center;">
                  <div style="display:flex; align-items:center; justify-content:space-between; width:100%">
                    <div style="display:flex; align-items:center; gap:8px">
                      <span class="badge">#${i+1}</span>
                      <strong>${r.candidate || 'Unknown'}</strong>
                    </div>
                    <div class="circle" style="--val:${r.atsScore}"><span>${r.atsScore}%</span></div>
                  </div>
                  <div style="display:flex; gap:8px">
                    <button class="btn" data-details-index="${i}">Details</button>
                    <button class="btn" data-preview-fn="${encodeURIComponent(r.file||'')}">View Resume</button>
                    <button class="btn" data-copy-index="${i}">Copy</button>
                  </div>
                </div>
              </div>
            </div>`).join('');

        // copy + preview + details handlers
        rankCards.querySelectorAll('button[data-copy-index]').forEach(btn => btn.addEventListener('click', (e) => {
          const i = parseInt(e.currentTarget.getAttribute('data-copy-index'), 10);
          const row = state.results[i]; if (!row) return;
          const text = `Name: ${row.candidate || ''}\nMobile: ${row.phone || ''}\nEmail: ${row.email || ''}`;
          navigator.clipboard.writeText(text).catch(()=>{});
        }));
        rankCards.querySelectorAll('button[data-details-index]').forEach(btn => btn.addEventListener('click', (e) => {
          const i = parseInt(e.currentTarget.getAttribute('data-details-index'), 10);
          const row = state.results[i]; if (!row) return;
          openDetails(row);
        }));
        rankCards.querySelectorAll('button[data-preview-fn]').forEach(btn => btn.addEventListener('click', (e) => {
          const fn = decodeURIComponent(e.currentTarget.getAttribute('data-preview-fn')||'');
          if (!fn) return;
          const f = filenameToFile.get(fn);
          if (f) {
            const url = URL.createObjectURL(f);
            window.open(url, '_blank');
            setTimeout(()=>URL.revokeObjectURL(url), 5000);
          } else {
            alert('Preview unavailable: file not found from upload list.');
          }
        }));

        // table removed per design; only cards are shown
      }

      // Export
      document.getElementById('exportCsvBtn').addEventListener('click', () => {
        if (!state.results.length) return;
        const headers = ['candidate','role','atsScore','skillMatch','file'];
        const csv = [headers.join(',')].concat(state.results.map(r => headers.map(h => JSON.stringify(r[h]??'')).join(','))).join('\n');
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a'); a.href = url; a.download = 'ats_results.csv'; a.click(); URL.revokeObjectURL(url);
      });
      // Details modal controls
      const detailsBackdrop = document.getElementById('detailsBackdrop');
      const detailsModal = document.getElementById('detailsModal');
      const detailsCloseBtn = document.getElementById('detailsCloseBtn');
      function openDetails(row) {
        document.getElementById('detailsTitle').textContent = row.candidate || 'Candidate';
        document.getElementById('detailsEmail').textContent = row.email || 'no-email';
        document.getElementById('detailsPhone').textContent = row.phone || 'no-phone';
        const a = row.analysis || {};
        document.getElementById('detailsSummary').innerHTML = `
          <div class="chips" style="margin-bottom:8px">
            <span class="chip">ATS ${a.ats_score ?? 0}</span>
            <span class="chip">Research ${a.researcher_potential_score ?? 0}</span>
          </div>
          <div><strong>Eligibility:</strong><br/>${(a.eligibility_for_role||'').toString()}</div>`;
        document.getElementById('detailsPotential').innerHTML = `
          <div><strong>Expected Potential:</strong><br/>${(a.expected_potential||'').toString()}</div>`;
        const found = (a.found_keywords||[]).map(k=>`<span class='chip'>${k}</span>`).join(' ');
        const missing = (a.missing_keywords||[]).map(k=>`<span class='chip'>${k}</span>`).join(' ');
        document.getElementById('detailsFoundKeywords').innerHTML = found || '<span class="chip">None</span>';
        document.getElementById('detailsMissingKeywords').innerHTML = missing || '<span class="chip">None</span>';
        const alts = (a.alternative_job_roles||[]).map(o=>`<span class='chip'>${o.role}: ${o.score}</span>`).join(' ');
        document.getElementById('detailsAltRoles').innerHTML = alts || '<span class="chip">None</span>';
        detailsBackdrop.style.display = 'block';
        detailsModal.style.display = 'flex';
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
      function closeDetails(){ detailsBackdrop.style.display = 'none'; detailsModal.style.display = 'none'; }
      detailsCloseBtn.addEventListener('click', closeDetails);
      detailsBackdrop.addEventListener('click', closeDetails);
      // JSON export removed per request
    </script>
  </body>
  </html>


